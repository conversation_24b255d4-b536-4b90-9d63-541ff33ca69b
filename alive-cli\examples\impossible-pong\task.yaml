name: "impossible-pong"
description: |
  Update index.html with the following features:
   - Add an overlaid styled popup to start the game on first load
   - Between each point, show a 3 second countdown (this should be skipped if a player wins)
   - After each game the AI wins, display text at the bottom of the screen with lighthearted insults for the player
   - Add a leaderboard to the right of the court that shows how many games each player has won.
   - When a player wins, a styled popup appears with the winner's name and the option to play again. The leaderboard should update.
   - Add an "even more insane" difficulty mode that adds spin to the ball that makes it harder to predict.
   - Add an "even more(!!) insane" difficulty mode where the ball does a spin mid court and then picks a random (reasonable) direction to go in (this should only advantage the AI player)
   - Let the user choose which difficulty mode they want to play in on the popup that appears when the game starts.
