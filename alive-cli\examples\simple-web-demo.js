#!/usr/bin/env node
/**
 * Simple Web Tool Demo Script
 * 
 * This script demonstrates the basic functionality of web requests
 * without relying on the Alive AI web tool implementation.
 */

import axios from 'axios';
import * as cheerio from 'cheerio';

// Helper function to print section headers
function printSection(title) {
  console.log('\n' + '='.repeat(80));
  console.log(`${title}`);
  console.log('='.repeat(80));
}

async function fetchUrl(url, options = {}) {
  try {
    const response = await axios({
      method: options.method || 'GET',
      url,
      headers: options.headers || {
        'User-Agent': 'AliveAI-Demo/1.0'
      },
      timeout: options.timeout || 10000,
      responseType: options.responseType || 'text'
    });
    
    return {
      success: true,
      status: response.status,
      data: response.data
    };
  } catch (error) {
    return {
      success: false,
      error: error.message,
      status: error.response?.status
    };
  }
}

async function scrapeWebpage(url, selector) {
  try {
    const response = await fetchUrl(url);
    
    if (!response.success) {
      return response;
    }
    
    const $ = cheerio.load(response.data);
    const content = selector ? $(selector).text().trim() : $('body').text().trim();
    
    return {
      success: true,
      content
    };
  } catch (error) {
    return {
      success: false,
      error: error.message
    };
  }
}

async function duckduckgoSearch(query, numResults = 5) {
  try {
    const url = `https://html.duckduckgo.com/html/?q=${encodeURIComponent(query)}`;
    const response = await fetchUrl(url);
    
    if (!response.success) {
      return response;
    }
    
    const $ = cheerio.load(response.data);
    const results = [];
    
    $('.result').each((i, element) => {
      if (results.length >= numResults) return false;
      
      const title = $(element).find('.result__title').text().trim();
      const url = $(element).find('.result__url').text().trim();
      const snippet = $(element).find('.result__snippet').text().trim();
      
      results.push({ title, url, snippet });
    });
    
    return {
      success: true,
      results
    };
  } catch (error) {
    return {
      success: false,
      error: error.message
    };
  }
}

async function runDemo() {
  try {
    printSection('1. Basic URL Fetch');
    console.log('Fetching content from https://httpbin.org/get...');
    const fetchResult = await fetchUrl('https://httpbin.org/get', {
      responseType: 'json'
    });
    
    if (fetchResult.success) {
      console.log(`Success: ${fetchResult.success}`);
      console.log(`Status: ${fetchResult.status}`);
      console.log('Data:', JSON.stringify(fetchResult.data, null, 2).substring(0, 300) + '...');
    } else {
      console.log(`Error: ${fetchResult.error}`);
    }
    
    printSection('2. Web Scraping');
    console.log('Scraping content from https://example.com...');
    const scrapeResult = await scrapeWebpage('https://example.com', 'h1, p');
    
    if (scrapeResult.success) {
      console.log(`Success: ${scrapeResult.success}`);
      console.log(`Content: ${scrapeResult.content}`);
    } else {
      console.log(`Error: ${scrapeResult.error}`);
    }
    
    printSection('3. DuckDuckGo Search');
    console.log('Searching for "Alive AI" on DuckDuckGo...');
    const searchResult = await duckduckgoSearch('Alive AI', 3);
    
    if (searchResult.success) {
      console.log(`Success: ${searchResult.success}`);
      console.log('Results:');
      searchResult.results.forEach((result, index) => {
        console.log(`\n${index + 1}. ${result.title}`);
        console.log(`   URL: ${result.url}`);
        console.log(`   Snippet: ${result.snippet}`);
      });
    } else {
      console.log(`Error: ${searchResult.error}`);
    }
    
    printSection('Demo Complete');
    console.log('The web tool demo has completed successfully.');
    console.log('To use the web tool in Alive AI, run:');
    console.log('  alive --enable-web-tool "your prompt here"');
    console.log('\nTo configure API keys, run:');
    console.log('  npm run manage-web-keys');
  } catch (error) {
    console.error('Error running demo:', error);
  }
}

// Run the demo
runDemo();
