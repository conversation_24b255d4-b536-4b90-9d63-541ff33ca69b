# Alive AI Web Tool

The Web Tool is a powerful feature in Alive AI that allows fetching real-time information from the internet. It provides capabilities for URL fetching, web scraping, API requests, and web searches using multiple search providers.

## Features

- **URL Fetching**: Retrieve content from any URL with customizable options
- **Web Scraping**: Extract specific content from webpages using CSS selectors
- **API Requests**: Make authenticated API calls with proper headers and parameters
- **Web Search**: Search the web using multiple providers:
  - Google (requires API key and Custom Search Engine ID)
  - Bing (requires API key)
  - DuckDuckGo (no API key required)
  - StackOverflow (no API key required)
  - GitHub (no API key required)
- **Caching**: Automatically cache results to reduce API usage and improve performance
- **Rate Limiting**: Built-in rate limiting to prevent API abuse

## Setup

### Enabling the Web Tool

To enable the Web Tool, use the `--enable-web-tool` flag when starting Alive AI:

```bash
alive --enable-web-tool "Your prompt here"
```

### Configuring API Keys

For search providers that require API keys (Google and Bing), you can configure them in several ways:

1. **Using the Web Tool Key Manager**:

   ```bash
   node scripts/manage-web-keys.js
   ```

2. **Using Command Line Arguments**:

   ```bash
   alive --enable-web-tool --google-api-key "YOUR_GOOGLE_API_KEY" --google-cx "YOUR_GOOGLE_CX" --bing-api-key "YOUR_BING_API_KEY" "Your prompt here"
   ```

3. **Using Environment Variables**:

   ```bash
   export GOOGLE_API_KEY="YOUR_GOOGLE_API_KEY"
   export GOOGLE_SEARCH_CX="YOUR_GOOGLE_CX"
   export BING_API_KEY="YOUR_BING_API_KEY"
   export ALIVE_DEFAULT_SEARCH_PROVIDER="google"
   
   alive --enable-web-tool "Your prompt here"
   ```

## Usage Examples

### Fetching a URL

```
fetch a webpage from https://example.com and summarize its content
```

### Scraping Specific Content

```
extract the main heading from https://example.com
```

### Making API Requests

```
get the latest weather data for New York City using a weather API
```

### Performing Web Searches

```
search for information about "quantum computing advances 2023"
```

### Using Search Filters

```
search for JavaScript tutorials on stackoverflow from the past month
```

## Advanced Configuration

### Customizing the Default Search Provider

You can set your preferred default search provider:

```bash
alive --enable-web-tool --search-provider "google" "Your prompt here"
```

Available options: `google`, `bing`, `duckduckgo`, `stackoverflow`, `github`

### Caching Configuration

By default, the Web Tool caches results for 1 hour to improve performance and reduce API usage. This behavior can be controlled through environment variables:

```bash
# Disable caching
export ALIVE_WEB_TOOL_CACHE_ENABLED=false

# Change cache TTL (in milliseconds)
export ALIVE_WEB_TOOL_CACHE_TTL=3600000  # 1 hour
```

### Rate Limiting Configuration

The Web Tool includes built-in rate limiting to prevent API abuse. The default limits are:

- Google: 10 requests per minute
- Bing: 10 requests per minute
- DuckDuckGo: 20 requests per minute
- Default: 60 requests per minute

These limits can be adjusted through environment variables:

```bash
export ALIVE_WEB_TOOL_RATE_LIMIT_GOOGLE=20
export ALIVE_WEB_TOOL_RATE_LIMIT_BING=20
export ALIVE_WEB_TOOL_RATE_LIMIT_DUCKDUCKGO=30
export ALIVE_WEB_TOOL_RATE_LIMIT_DEFAULT=100
```

## Obtaining API Keys

### Google Custom Search API

1. Create a project in the [Google Cloud Console](https://console.cloud.google.com/)
2. Enable the "Custom Search API"
3. Create API credentials
4. Create a [Custom Search Engine](https://cse.google.com/cse/all) and note the CX value

### Bing Web Search API

1. Sign up for the [Bing Web Search API](https://www.microsoft.com/en-us/bing/apis/bing-web-search-api) through Azure
2. Create a resource and note your API key

## Troubleshooting

### Common Issues

1. **Rate Limit Exceeded**: If you see "Rate limit exceeded" errors, wait a minute before trying again or adjust the rate limit configuration.

2. **API Key Issues**: Ensure your API keys are correctly configured and have the necessary permissions.

3. **Search Provider Not Available**: If a search provider is not working, try switching to another provider using the `--search-provider` flag.

### Logs

To see detailed logs for the Web Tool, set the `DEBUG` environment variable:

```bash
export DEBUG=alive:web-tool
```

## Implementation Details

The Web Tool is implemented as an MCP (Model Context Protocol) server that provides tools for web operations. It integrates with the Alive AI CLI and can be used by the AI model to fetch information from the internet.

For developers interested in extending the Web Tool, the main implementation files are:

- `src/utils/web-tool.ts`: Core implementation of web tool functions
- `src/utils/get-web-tool-keys.tsx`: API key management
- `src/mcp-servers/web-tool-server.ts`: MCP server implementation
- `scripts/manage-web-keys.js`: Command-line tool for managing API keys
