# Web Tools for Alive AI

Alive AI includes powerful web-based tools that allow the AI to fetch real-time information from the internet. These tools are enabled by default and provide the following capabilities:

## Available Web Tools

### 1. Fetch

The `fetch` tool allows the AI to retrieve content from a URL with specified options.

```
fetch(url, options)
```

**Parameters:**
- `url`: The URL to fetch
- `method`: HTTP method (GET, POST, PUT, DELETE, etc.), default is GET
- `body`: Request body for POST, PUT, etc.
- `headers`: HTTP headers to include in the request
- `responseType`: Response type (json, text, arraybuffer, blob), default is text
- `timeout`: Request timeout in milliseconds
- `followRedirects`: Whether to follow redirects, default is true
- `maxRedirects`: Maximum number of redirects to follow, default is 5
- `cacheResults`: Whether to cache results, default is true

### 2. Scrape

The `scrape` tool allows the AI to extract content from a webpage with optional selector filtering.

```
scrape(url, options)
```

**Parameters:**
- `url`: The URL to scrape
- `selector`: CSS selector to extract specific content
- `extractContents`: Extract text content only (true) or full HTML (false), default is true
- `timeout`: Request timeout in milliseconds
- `headers`: HTTP headers to include in the request
- `cacheResults`: Whether to cache results, default is true

### 3. API

The `api` tool allows the AI to make API requests with appropriate authentication and parameters.

```
api(url, options)
```

**Parameters:**
- `url`: The base URL for the API
- `method`: HTTP method (GET, POST, PUT, DELETE, etc.), default is GET
- `endpoint`: API endpoint to append to the base URL
- `body`: Request body for POST, PUT, etc.
- `headers`: HTTP headers to include in the request
- `apiKey`: API key for authentication
- `responseType`: Response type (json, text, arraybuffer, blob), default is json
- `timeout`: Request timeout in milliseconds
- `cacheResults`: Whether to cache results, default is true

### 4. Search

The `search` tool allows the AI to perform web searches using various providers.

```
search(options)
```

**Parameters:**
- `provider`: Search provider (google, bing, microsoft, yahoo, duckduckgo, stackoverflow, github), default is duckduckgo
- `query`: Search query
- `apiKey`: API key for the search provider (required for Google and Bing)
- `cx`: Google Custom Search Engine ID (required for Google)
- `numResults`: Number of results to return, default is 5
- `cacheResults`: Whether to cache results, default is true
- `filters`: Search filters (timeRange, siteRestriction, fileType, language)

## Configuration

Web tools are enabled by default in Alive AI. You can configure the web tools using the following methods:

### Command Line Options

```
--enable-web-tool         Enable the web tool for fetching data from the internet
                          (enabled by default)
--google-api-key <key>    Set the Google API key for web search
--bing-api-key <key>      Set the Bing API key for web search
--google-cx <id>          Set the Google Custom Search Engine ID for search
--search-provider <provider> Set the default search provider (google, bing, duckduckgo)
```

### Configuration File

You can also configure web tools in the `~/.alive-ai/config.json` file:

```json
{
  "enableWebTool": true,
  "webTool": {
    "googleApiKey": "your-google-api-key",
    "bingApiKey": "your-bing-api-key",
    "googleSearchCx": "your-google-cx-id",
    "defaultSearchProvider": "duckduckgo"
  }
}
```

### Environment Variables

Web tool configuration can also be set using environment variables:

- `GOOGLE_API_KEY`: Google API key for search operations
- `BING_API_KEY`: Bing API key for search operations
- `GOOGLE_SEARCH_CX`: Google Custom Search Engine ID
- `ALIVE_DEFAULT_SEARCH_PROVIDER`: Default search provider (google, bing, duckduckgo)

## Search Providers

### DuckDuckGo (Default)

DuckDuckGo is the default search provider and does not require an API key.

### Google

To use Google search, you need:
1. A Google API key from the [Google Cloud Console](https://console.cloud.google.com/)
2. A Custom Search Engine ID (cx) from the [Google Programmable Search Engine](https://programmablesearchengine.google.com/)

### Bing

To use Bing search, you need a Bing API key from the [Microsoft Azure Portal](https://portal.azure.com/).

### Other Providers

The following providers are also supported and do not require API keys:
- Microsoft (uses Bing API key)
- Yahoo
- StackOverflow
- GitHub

## Rate Limiting and Caching

Web tools include built-in rate limiting and caching to prevent abuse and improve performance:

- Rate limits are applied per provider (e.g., 10 requests per minute for Google)
- Results are cached for 1 hour by default
- Cache can be disabled per request using the `cacheResults: false` option

## Examples

### Fetching a URL
```
fetch('https://example.com')
```

### Scraping content with a selector
```
scrape('https://example.com', { selector: 'h1', extractContents: true })
```

### Making an API request
```
api('https://api.example.com', { 
  endpoint: '/data', 
  method: 'POST', 
  body: JSON.stringify({ key: 'value' }),
  headers: { 'Content-Type': 'application/json' }
})
```

### Performing a search
```
search({ 
  provider: 'duckduckgo', 
  query: 'Alive AI web tools', 
  numResults: 5 
})
```
