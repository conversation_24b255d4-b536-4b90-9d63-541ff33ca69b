#!/usr/bin/env node
// Import dynamically to handle different build configurations
import { promises as fs } from 'fs';
import { dirname, join, resolve } from 'path';
import { fileURLToPath, pathToFileURL } from 'url';

// Get current directory
const __dirname = dirname(fileURLToPath(import.meta.url));

async function importWebTool() {
  console.log('Trying to import web-tool module...');
  
  // Try different potential paths
  const paths = [
    '../src/utils/web-tool.js',
    '../dist/utils/web-tool.js',
    '../dist/cli.js'
  ];
  
  for (const importPath of paths) {
    try {
      const fullPath = resolve(__dirname, importPath);
      // Convert Windows path to file:// URL format for ESM
      const fileUrl = pathToFileURL(fullPath).href;
      console.log(`Attempting to import from: ${fileUrl}`);
      
      // For the bundled CLI, try to extract the web tool exports
      if (importPath.includes('cli.js')) {
        // The web tool functions are likely bundled in the CLI
        console.log('Attempting to use the CLI bundle...');
        
        // Just import everything that's exported and see what's available
        const module = await import(fileUrl);
        console.log('Available exports:', Object.keys(module));
        
        // Continue with other import attempts
        continue;
      }
      
      const module = await import(fileUrl);
      console.log('Successfully imported web-tool module!');
      return module;
    } catch (err) {
      console.log(`Import failed for ${importPath}: ${err.message}`);
    }
  }
  
  throw new Error('Could not import web-tool module from any known location');
}

/**
 * Simple CLI demo of the web tool functionality
 */
async function runDemo() {
  console.log('Alive AI Web Tool Demo');
  console.log('=====================\n');

  try {
    // First, dynamically import the web tool
    const { fetchUrl, scrapeWebpage, makeApiRequest, performSearch } = await importWebTool();
    
    // Demo 1: Simple URL fetch
    console.log('1. Fetching content from a URL...');
    const fetchResult = await fetchUrl('https://httpbin.org/get', 'GET', undefined, {
      headers: {
        'User-Agent': 'Alive-AI-Demo/1.0',
      },
      responseType: 'json',
      timeout: 10000,
    });
    
    console.log(`Status: ${fetchResult.success ? 'Success' : 'Failed'}`);
    if (fetchResult.error) {
      console.error(`Error: ${fetchResult.error}`);
    }
    console.log(`Response:\n${fetchResult.output.slice(0, 300)}${fetchResult.output.length > 300 ? '...' : ''}\n`);

    // Save the response to a file
    const outputDir = join(__dirname, 'output');
    try {
      await fs.mkdir(outputDir, { recursive: true });
      await fs.writeFile(join(outputDir, 'fetch-result.json'), fetchResult.output);
      console.log(`Response saved to ${join(outputDir, 'fetch-result.json')}\n`);
    } catch (err) {
      console.log(`Could not save response: ${err.message}\n`);
    }

    // Demo 2: Web scraping with different selectors
    console.log('2. Scraping webpage content...');
    const scrapeResult = await scrapeWebpage('https://news.ycombinator.com', {
      selector: '.titleline',
      extractContents: true,
      timeout: 10000,
    });
    
    console.log(`Status: ${scrapeResult.success ? 'Success' : 'Failed'}`);
    if (scrapeResult.error) {
      console.error(`Error: ${scrapeResult.error}`);
    }
    console.log(`Extracted titles (first 300 chars):\n${scrapeResult.output.slice(0, 300)}${scrapeResult.output.length > 300 ? '...' : ''}\n`);

    // Demo 2b: Scrape with a different selector
    console.log('2b. Scraping with a different selector...');
    const scrapeResult2 = await scrapeWebpage('https://news.ycombinator.com', {
      selector: '.score',
      extractContents: true,
    });
    
    console.log(`Status: ${scrapeResult2.success ? 'Success' : 'Failed'}`);
    if (scrapeResult2.error) {
      console.error(`Error: ${scrapeResult2.error}`);
    }
    console.log(`Extracted scores (first 300 chars):\n${scrapeResult2.output.slice(0, 300)}${scrapeResult2.output.length > 300 ? '...' : ''}\n`);

    // Demo 3: API request
    console.log('3. Making an API request...');
    const apiResult = await makeApiRequest('https://httpbin.org/json', 'GET', undefined, {
      responseType: 'json',
      timeout: 10000,
    });
    
    console.log(`Status: ${apiResult.success ? 'Success' : 'Failed'}`);
    if (apiResult.error) {
      console.error(`Error: ${apiResult.error}`);
    }
    console.log(`API response:\n${apiResult.output.slice(0, 300)}${apiResult.output.length > 300 ? '...' : ''}\n`);

    // Demo 3b: POST request with data
    console.log('3b. Making a POST API request with data...');
    const postData = JSON.stringify({
      name: 'Alive AI',
      type: 'CLI Tool',
      demo: true
    });
    
    const postApiResult = await makeApiRequest('https://httpbin.org/post', 'POST', postData, {
      headers: {
        'Content-Type': 'application/json',
      },
      responseType: 'json',
    });
    
    console.log(`Status: ${postApiResult.success ? 'Success' : 'Failed'}`);
    if (postApiResult.error) {
      console.error(`Error: ${postApiResult.error}`);
    }
    console.log(`API POST response:\n${postApiResult.output.slice(0, 300)}${postApiResult.output.length > 300 ? '...' : ''}\n`);

    // Demo 4: Web search (using DuckDuckGo which doesn't require an API key)
    console.log('4. Performing a web search (DuckDuckGo)...');
    const searchResult = await performSearch({
      provider: 'duckduckgo',
      query: 'Alive AI CLI tool',
      numResults: 3
    });
    
    console.log(`Status: ${searchResult.success ? 'Success' : 'Failed'}`);
    if (searchResult.error) {
      console.error(`Error: ${searchResult.error}`);
    }
    console.log(`Search results:\n${searchResult.output}\n`);

    // Demo 5: Google search if API key is available
    if (process.env.GOOGLE_API_KEY && process.env.GOOGLE_SEARCH_CX) {
      console.log('5. Performing a web search (Google)...');
      const googleResult = await performSearch({
        provider: 'google',
        query: 'Alive AI CLI tool',
        apiKey: process.env.GOOGLE_API_KEY,
        numResults: 3
      });
      
      console.log(`Status: ${googleResult.success ? 'Success' : 'Failed'}`);
      if (googleResult.error) {
        console.error(`Error: ${googleResult.error}`);
      }
      console.log(`Search results:\n${googleResult.output}\n`);
    } else {
      console.log('5. Skipping Google search (GOOGLE_API_KEY and GOOGLE_SEARCH_CX environment variables not set)\n');
    }

    // Demo 6: Bing search if API key is available
    if (process.env.BING_API_KEY) {
      console.log('6. Performing a web search (Bing)...');
      const bingResult = await performSearch({
        provider: 'bing',
        query: 'Alive AI CLI tool',
        apiKey: process.env.BING_API_KEY,
        numResults: 3
      });
      
      console.log(`Status: ${bingResult.success ? 'Success' : 'Failed'}`);
      if (bingResult.error) {
        console.error(`Error: ${bingResult.error}`);
      }
      console.log(`Search results:\n${bingResult.output}\n`);
    } else {
      console.log('6. Skipping Bing search (BING_API_KEY environment variable not set)\n');
    }

    // Demo 7: Error handling example - invalid URL
    console.log('7. Error handling example - invalid URL...');
    const invalidUrlResult = await fetchUrl('https://this-domain-should-not-exist-alive-ai-demo.xyz');
    
    console.log(`Status: ${invalidUrlResult.success ? 'Success' : 'Failed'}`);
    console.log(`Error: ${invalidUrlResult.error || 'No error'}\n`);

    console.log('\n✅ Demo completed successfully!');
    console.log('For more information, see the documentation in docs/web-tool.md');

  } catch (err) {
    console.error('\n❌ Error running demo:', err);
    process.exit(1);
  }
}

runDemo().catch(err => {
  console.error('Error running demo:', err);
  process.exit(1);
}); 