{"version": 3, "file": "builtinSymbolLikes.js", "sourceRoot": "", "sources": ["../src/builtinSymbolLikes.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAYA,sCAEC;AAUD,4DAKC;AAUD,kCAEC;AASD,kDAWC;AASD,gDAeC;AACD,wDA+BC;AAED,kDAwBC;AAED,kEAkCC;AAnLD,+CAAiC;AAEjC,6EAA0E;AAE1E;;;;;;;GAOG;AACH,SAAgB,aAAa,CAAC,OAAmB,EAAE,IAAa;IAC9D,OAAO,mBAAmB,CAAC,OAAO,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;AACvD,CAAC;AAED;;;;;;;GAOG;AACH,SAAgB,wBAAwB,CACtC,OAAmB,EACnB,IAAa;IAEb,OAAO,mBAAmB,CAAC,OAAO,EAAE,IAAI,EAAE,oBAAoB,CAAC,CAAC;AAClE,CAAC;AAED;;;;;;;GAOG;AACH,SAAgB,WAAW,CAAC,OAAmB,EAAE,IAAa;IAC5D,OAAO,mBAAmB,CAAC,OAAO,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;AACrD,CAAC;AAED;;;;;;GAMG;AACH,SAAgB,mBAAmB,CACjC,OAAmB,EACnB,IAAa;IAEb,OAAO,kBAAkB,CAAC,OAAO,EAAE,IAAI,EAAE,OAAO,CAAC,EAAE;QACjD,MAAM,CAAC,YAAY,CAAC,GAAG,OAAO,CAAC,kBAAkB,CAAC;QAClD,OAAO,CACL,WAAW,CAAC,OAAO,EAAE,YAAY,CAAC;YAClC,mBAAmB,CAAC,OAAO,EAAE,YAAY,CAAC,CAC3C,CAAC;IACJ,CAAC,CAAC,CAAC;AACL,CAAC;AAED;;;;;;GAMG;AACH,SAAgB,kBAAkB,CAChC,OAAmB,EACnB,IAAa,EACb,SAKY;IAEZ,OAAO,sBAAsB,CAAC,OAAO,EAAE,IAAI,EAAE,OAAO,CAAC,EAAE;QACrD,OAAO,CACL,OAAO,CAAC,WAAW,CAAC,OAAO,EAAE,KAAK,UAAU,IAAI,CAAC,CAAC,SAAS,EAAE,CAAC,OAAO,CAAC,CACvE,CAAC;IACJ,CAAC,CAAC,CAAC;AACL,CAAC;AACD,SAAgB,sBAAsB,CACpC,OAAmB,EACnB,IAAa,EACb,SAKY;IAEZ,OAAO,2BAA2B,CAAC,OAAO,EAAE,IAAI,EAAE,OAAO,CAAC,EAAE;QAC1D,MAAM,EAAE,WAAW,EAAE,kBAAkB,EAAE,GAAG,OAAO,CAAC;QAEpD,IAAI,CAAC,WAAW,IAAI,CAAC,kBAAkB,EAAE,CAAC;YACxC,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IACE,IAAA,uDAA0B,EAAC,OAAO,EAAE,WAAW,CAAC;YAChD,SAAS,CACP,OAGC,CACF,EACD,CAAC;YACD,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC,CAAC,CAAC;AACL,CAAC;AAED,SAAgB,mBAAmB,CACjC,OAAmB,EACnB,IAAa,EACb,UAA6B;IAE7B,OAAO,2BAA2B,CAAC,OAAO,EAAE,IAAI,EAAE,OAAO,CAAC,EAAE;QAC1D,MAAM,MAAM,GAAG,OAAO,CAAC,SAAS,EAAE,CAAC;QACnC,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,OAAO,KAAK,CAAC;QACf,CAAC;QAED,MAAM,gBAAgB,GAAG,MAAM,CAAC,OAAO,EAAE,CAAC;QAE1C,IACE,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC;YACxB,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,gBAAgB,KAAK,IAAI,CAAC;YACpD,CAAC,CAAC,gBAAgB,KAAK,UAAU,CAAC;YACpC,IAAA,uDAA0B,EAAC,OAAO,EAAE,MAAM,CAAC,EAC3C,CAAC;YACD,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC,CAAC,CAAC;AACL,CAAC;AAED,SAAgB,2BAA2B,CACzC,OAAmB,EACnB,IAAa,EACb,SAA+C;IAE/C,IAAI,IAAI,CAAC,cAAc,EAAE,EAAE,CAAC;QAC1B,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CACzB,2BAA2B,CAAC,OAAO,EAAE,CAAC,EAAE,SAAS,CAAC,CACnD,CAAC;IACJ,CAAC;IACD,IAAI,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC;QACnB,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAC1B,2BAA2B,CAAC,OAAO,EAAE,CAAC,EAAE,SAAS,CAAC,CACnD,CAAC;IACJ,CAAC;IAED,MAAM,eAAe,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;IACxC,IAAI,OAAO,eAAe,KAAK,SAAS,EAAE,CAAC;QACzC,OAAO,eAAe,CAAC;IACzB,CAAC;IAED,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;IAChC,IACE,MAAM;QACN,MAAM,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC,WAAW,CAAC,KAAK,GAAG,EAAE,CAAC,WAAW,CAAC,SAAS,CAAC,EAChE,CAAC;QACD,MAAM,OAAO,GAAG,OAAO,CAAC,cAAc,EAAE,CAAC;QACzC,KAAK,MAAM,QAAQ,IAAI,OAAO,CAAC,YAAY,CAAC,IAAwB,CAAC,EAAE,CAAC;YACtE,IAAI,2BAA2B,CAAC,OAAO,EAAE,QAAQ,EAAE,SAAS,CAAC,EAAE,CAAC;gBAC9D,OAAO,IAAI,CAAC;YACd,CAAC;QACH,CAAC;IACH,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC"}